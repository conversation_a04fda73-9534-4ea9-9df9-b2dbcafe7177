[gd_scene load_steps=12 format=3]

[ext_resource type="PackedScene" path="res://enemies/melee_enemy_3d.tscn" id="1_melee"]
[ext_resource type="PackedScene" path="res://enemies/charger_enemy_3d.tscn" id="2_charger"]
[ext_resource type="PackedScene" path="res://enemies/ranged_enemy_3d.tscn" id="3_ranged"]

[sub_resource type="Environment" id="Environment_1"]
background_mode = 1
background_color = Color(0.2, 0.2, 0.3, 1)
ambient_light_source = 2
ambient_light_color = Color(0.8, 0.8, 1, 1)
ambient_light_energy = 0.3

[sub_resource type="PlaneMesh" id="PlaneMesh_1"]
size = Vector2(50, 50)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ground"]
albedo_color = Color(0.3, 0.5, 0.3, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_ground"]
size = Vector3(50, 0.1, 50)

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_player"]

[sub_resource type="CapsuleMesh" id="CapsuleMesh_player"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_player"]
albedo_color = Color(0, 0.8, 1, 1)
emission_enabled = true
emission = Color(0, 0.3, 0.5, 1)

[sub_resource type="GDScript" id="GDScript_player"]
script/source = "extends CharacterBody3D

@export var speed: float = 8.0
@export var jump_velocity: float = 8.0
@export var mouse_sensitivity: float = 0.002

var gravity: float = 20.0
var health: float = 100.0

@onready var camera: Camera3D = $Camera3D
@onready var health_label: Label3D = $HealthLabel

func _ready() -> void:
	add_to_group(\"player\")
	Input.mouse_mode = Input.MOUSE_MODE_CAPTURED

func _input(event: InputEvent) -> void:
	if event is InputEventMouseMotion and Input.mouse_mode == Input.MOUSE_MODE_CAPTURED:
		rotate_y(-event.relative.x * mouse_sensitivity)
		camera.rotate_x(-event.relative.y * mouse_sensitivity)
		camera.rotation.x = clamp(camera.rotation.x, -PI/2, PI/2)
	
	if event.is_action_pressed(\"ui_cancel\"):
		if Input.mouse_mode == Input.MOUSE_MODE_CAPTURED:
			Input.mouse_mode = Input.MOUSE_MODE_VISIBLE
		else:
			Input.mouse_mode = Input.MOUSE_MODE_CAPTURED

func _physics_process(delta: float) -> void:
	# Gravity
	if not is_on_floor():
		velocity.y -= gravity * delta
	
	# Jump
	if Input.is_action_just_pressed(\"ui_accept\") and is_on_floor():
		velocity.y = jump_velocity
	
	# Movement
	var input_dir = Input.get_vector(\"ui_left\", \"ui_right\", \"ui_up\", \"ui_down\")
	var direction = (transform.basis * Vector3(input_dir.x, 0, input_dir.y)).normalized()
	if direction:
		velocity.x = direction.x * speed
		velocity.z = direction.z * speed
	else:
		velocity.x = move_toward(velocity.x, 0, speed)
		velocity.z = move_toward(velocity.z, 0, speed)
	
	move_and_slide()
	
	# Update health display
	if health_label:
		health_label.text = \"Health: %.0f\" % health

func take_damage(amount: float) -> void:
	health -= amount
	print(\"Player took \", amount, \" damage. Health: \", health)
	if health <= 0:
		die()

func take_hit() -> void:
	take_damage(10.0)

func die() -> void:
	print(\"Player died!\")
	# Simple respawn
	global_position = Vector3(0, 2, 0)
	health = 100.0
"

[sub_resource type="NavigationMesh" id="NavigationMesh_1"]
vertices = PackedFloat32Array(-25, 0.1, -25, -25, 0.1, 25, 25, 0.1, 25, 25, 0.1, -25)
polygons = [PackedInt32Array(3, 2, 0), PackedInt32Array(0, 2, 1)]

[node name="EnemyTest3D" type="Node3D"]

[node name="Environment" type="Node3D" parent="."]

[node name="WorldEnvironment" type="WorldEnvironment" parent="Environment"]
environment = SubResource("Environment_1")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="Environment"]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 10, 0)
light_energy = 0.8
shadow_enabled = true

[node name="Ground" type="StaticBody3D" parent="Environment"]

[node name="MeshInstance3D" type="MeshInstance3D" parent="Environment/Ground"]
mesh = SubResource("PlaneMesh_1")
surface_material_override/0 = SubResource("StandardMaterial3D_ground")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/Ground"]
shape = SubResource("BoxShape3D_ground")

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="Environment"]
navigation_mesh = SubResource("NavigationMesh_1")

[node name="Player" type="CharacterBody3D" parent="." groups=["player"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2, 0)
script = SubResource("GDScript_player")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Player"]
shape = SubResource("CapsuleShape3D_player")

[node name="MeshInstance3D" type="MeshInstance3D" parent="Player"]
mesh = SubResource("CapsuleMesh_player")
surface_material_override/0 = SubResource("StandardMaterial3D_player")

[node name="Camera3D" type="Camera3D" parent="Player"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.5, 0)

[node name="HealthLabel" type="Label3D" parent="Player"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.5, 0)
billboard = 1
text = "Health: 100"
font_size = 20
outline_size = 6

[node name="Enemies" type="Node3D" parent="."]

[node name="MeleeEnemy" parent="Enemies" instance=ExtResource("1_melee")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, 0)

[node name="ChargerEnemy" parent="Enemies" instance=ExtResource("2_charger")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 0, 0)

[node name="RangedEnemy" parent="Enemies" instance=ExtResource("3_ranged")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -12)

[node name="UI" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2

[node name="Instructions" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -120.0
offset_right = 400.0
offset_bottom = -20.0
text = "3D LimboAI Enemy Test Scene

WASD - Move
Mouse - Look around
Space - Jump
ESC - Toggle mouse capture

Red Enemy - Melee (chases and attacks up close)
Green Enemy - Charger (flanks then charges)
Blue Enemy - Ranged (shoots projectiles)"
