[gd_resource type="BehaviorTree" load_steps=41 format=3 uid="uid://cqluon1y1hnn5"]

[ext_resource type="Script" uid="uid://ccr43pgd4488l" path="res://Demo/ai/tasks/get_first_in_group.gd" id="1_4xk1i"]
[ext_resource type="Script" uid="uid://dbhd2dvt77sm4" path="res://Demo/ai/tasks/is_aligned_with_target.gd" id="2_a8qex"]
[ext_resource type="Script" uid="uid://df82exuqnfdb2" path="res://Demo/ai/tasks/arrive_pos.gd" id="3_q4r2p"]
[ext_resource type="Script" uid="uid://ct71h72pech3b" path="res://Demo/ai/tasks/select_flanking_pos.gd" id="4_53hao"]
[ext_resource type="Script" uid="uid://dbo0kq2cwb4qv" path="res://Demo/ai/tasks/face_target.gd" id="5_aexyq"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_46tbn"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"
var/run_speed/name = &"run_speed"
var/run_speed/type = 3
var/run_speed/value = 600.0
var/run_speed/hint = 1
var/run_speed/hint_string = "10,1000,10"

[sub_resource type="BBNode" id="BBNode_nrd4b"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_qiw21"]
animation_player = SubResource("BBNode_nrd4b")
animation_name = &"idle"
blend = 0.1

[sub_resource type="BTRandomWait" id="BTRandomWait_xlud8"]
min_duration = 0.7
max_duration = 1.5

[sub_resource type="BTAction" id="BTAction_c4cxo"]
script = ExtResource("1_4xk1i")
group = &"player"
output_var = &"target"

[sub_resource type="BTSequence" id="BTSequence_yhjh1"]
custom_name = "Take a break"
children = [SubResource("BTPlayAnimation_qiw21"), SubResource("BTRandomWait_xlud8"), SubResource("BTAction_c4cxo")]

[sub_resource type="BBNode" id="BBNode_ok0r5"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_unftu"]
animation_player = SubResource("BBNode_ok0r5")
animation_name = &"walk"
blend = 0.1
speed = 1.5

[sub_resource type="BTAction" id="BTAction_op6l6"]
script = ExtResource("4_53hao")
target_var = &"target"
flank_side = 1
range_min = 400
range_max = 1000
position_var = &"pos"

[sub_resource type="BTAction" id="BTAction_ycjun"]
script = ExtResource("3_q4r2p")
target_position_var = &"pos"
speed_var = &"run_speed"
tolerance = 50.0
avoid_var = &"target"

[sub_resource type="BTTimeLimit" id="BTTimeLimit_gadc6"]
time_limit = 7.0
children = [SubResource("BTAction_ycjun")]

[sub_resource type="BTAction" id="BTAction_poqpu"]
script = ExtResource("5_aexyq")
target_var = &"target"

[sub_resource type="BTSequence" id="BTSequence_0gdqn"]
custom_name = "Change flank"
children = [SubResource("BTPlayAnimation_unftu"), SubResource("BTAction_op6l6"), SubResource("BTTimeLimit_gadc6"), SubResource("BTAction_poqpu")]

[sub_resource type="BTCooldown" id="BTCooldown_2lneu"]
duration = 7.0
children = [SubResource("BTSequence_0gdqn")]

[sub_resource type="BTProbability" id="BTProbability_6sydk"]
run_chance = 0.3
children = [SubResource("BTCooldown_2lneu")]

[sub_resource type="BTAction" id="BTAction_kuuw2"]
script = ExtResource("4_53hao")
target_var = &"target"
flank_side = 0
range_min = 400
range_max = 1000
position_var = &"shoot_pos"

[sub_resource type="BBNode" id="BBNode_kc64r"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_panch"]
animation_player = SubResource("BBNode_kc64r")
animation_name = &"walk"
blend = 0.1

[sub_resource type="BTAction" id="BTAction_66hsk"]
script = ExtResource("3_q4r2p")
target_position_var = &"shoot_pos"
speed_var = &"speed"
tolerance = 50.0
avoid_var = &""

[sub_resource type="BTTimeLimit" id="BTTimeLimit_24ath"]
children = [SubResource("BTAction_66hsk")]

[sub_resource type="BTAction" id="BTAction_enw2m"]
script = ExtResource("5_aexyq")
target_var = &"target"

[sub_resource type="BTCondition" id="BTCondition_1fnyc"]
script = ExtResource("2_a8qex")
target_var = &"target"
tolerance = 150.0

[sub_resource type="BBNode" id="BBNode_s6vt4"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_qa8jy"]
animation_player = SubResource("BBNode_s6vt4")
animation_name = &"throw_prepare"
blend = 0.1

[sub_resource type="BTWait" id="BTWait_gbcyb"]

[sub_resource type="BBNode" id="BBNode_qkfqt"]
saved_value = NodePath("AnimationPlayer")
resource_name = "AnimationPlayer"

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_0ktds"]
await_completion = 1.0
animation_player = SubResource("BBNode_qkfqt")
animation_name = &"throw"
blend = 0.05

[sub_resource type="BBNode" id="BBNode_1yxc5"]
saved_value = NodePath(".")
resource_name = "."

[sub_resource type="BTCallMethod" id="BTCallMethod_yx4fk"]
node = SubResource("BBNode_1yxc5")
method = &"throw_ninja_star"

[sub_resource type="BTWait" id="BTWait_d2ib4"]
duration = 0.2

[sub_resource type="BTSequence" id="BTSequence_rgbq3"]
custom_name = "Throw ninja star"
children = [SubResource("BTPlayAnimation_qa8jy"), SubResource("BTWait_gbcyb"), SubResource("BTPlayAnimation_0ktds"), SubResource("BTCallMethod_yx4fk"), SubResource("BTWait_d2ib4")]
metadata/_weight_ = 1.0

[sub_resource type="BTRepeat" id="BTRepeat_g08ia"]
times = 3
children = [SubResource("BTSequence_rgbq3")]

[sub_resource type="BTSequence" id="BTSequence_lhg7f"]
custom_name = "Get into position"
children = [SubResource("BTAction_kuuw2"), SubResource("BTPlayAnimation_panch"), SubResource("BTTimeLimit_24ath"), SubResource("BTAction_enw2m"), SubResource("BTCondition_1fnyc"), SubResource("BTRepeat_g08ia")]
metadata/_weight_ = 1.0

[sub_resource type="BTSelector" id="BTSelector_1rrya"]
children = [SubResource("BTProbability_6sydk"), SubResource("BTSequence_lhg7f")]

[sub_resource type="BTSequence" id="BTSequence_pxl2k"]
custom_name = "Main"
children = [SubResource("BTSequence_yhjh1"), SubResource("BTSelector_1rrya")]

[resource]
description = "Here, the [dec]Probability[/dec] decorator permits the execution of its branch in 30% of cases. This introduces a slight variation to the behavior each time the \"Change flank\" sequence is considered by the [comp]Selector[/comp].

The [dec]Repeat[/dec] decorator is handy for doing something multiple times within a behavior tree. In our example, it's used to make the [comp]Throw ninja star[/comp] sequence perform three times in a row."
blackboard_plan = SubResource("BlackboardPlan_46tbn")
root_task = SubResource("BTSequence_pxl2k")
