[gd_resource type="BehaviorTree" load_steps=15 format=3]

[ext_resource type="Script" path="res://enemies/ai/tasks/get_first_in_group_3d.gd" id="1_657p6"]
[ext_resource type="Script" path="res://enemies/ai/tasks/select_flanking_pos_3d.gd" id="2_t3udh"]
[ext_resource type="Script" path="res://enemies/ai/tasks/arrive_pos_3d.gd" id="3_u2ra5"]
[ext_resource type="Script" path="res://enemies/ai/tasks/face_target_3d.gd" id="4_xwjl7"]
[ext_resource type="Script" path="res://enemies/ai/tasks/move_forward_3d.gd" id="5_ucvak"]
[ext_resource type="Script" path="res://enemies/ai/tasks/in_range_3d.gd" id="6_range"]
[ext_resource type="Script" path="res://enemies/ai/tasks/attack_melee_3d.gd" id="7_attack"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_charger"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 7.0
var/speed/hint = 1
var/speed/hint_string = "1,20,0.5"

[sub_resource type="BTAction" id="BTAction_get_target"]
script = ExtResource("1_657p6")
group_name = &"player"
output_var = &"target"

[sub_resource type="BTAction" id="BTAction_select_flank"]
script = ExtResource("2_t3udh")
target_var = &"target"
pos_var = &"flanking_pos"
flank_distance = 8.0
side_offset = 0.7

[sub_resource type="BTAction" id="BTAction_move_to_flank"]
script = ExtResource("3_u2ra5")
pos_var = &"flanking_pos"
speed_var = &"speed"
tolerance = 1.5

[sub_resource type="BTAction" id="BTAction_face_target"]
script = ExtResource("4_xwjl7")
target_var = &"target"

[sub_resource type="BTAction" id="BTAction_charge"]
script = ExtResource("5_ucvak")
speed_var = &"speed"
duration = 1.0

[sub_resource type="BTCondition" id="BTCondition_in_range"]
script = ExtResource("6_range")
target_var = &"target"
range = 2.5

[sub_resource type="BTAction" id="BTAction_attack"]
script = ExtResource("7_attack")
target_var = &"target"
attack_range = 2.5
damage = 35.0

[sub_resource type="BTSequence" id="BTSequence_setup_flank"]
custom_name = "Setup flanking position"
children = [SubResource("BTAction_select_flank"), SubResource("BTAction_move_to_flank")]

[sub_resource type="BTSequence" id="BTSequence_charge_attack"]
custom_name = "Charge and attack"
children = [SubResource("BTAction_face_target"), SubResource("BTAction_charge")]

[sub_resource type="BTSequence" id="BTSequence_melee_attack"]
custom_name = "Melee attack"
children = [SubResource("BTCondition_in_range"), SubResource("BTAction_attack")]

[sub_resource type="BTSelector" id="BTSelector_combat"]
custom_name = "Combat behavior"
children = [SubResource("BTSequence_melee_attack"), SubResource("BTSequence_charge_attack")]

[sub_resource type="BTSequence" id="BTSequence_main"]
custom_name = "Main behavior"
children = [SubResource("BTAction_get_target"), SubResource("BTSequence_setup_flank"), SubResource("BTSelector_combat")]

[resource]
description = "3D Charger Enemy - Tactical charging AI that flanks and rushes the player with higher damage."
blackboard_plan = SubResource("BlackboardPlan_charger")
root_task = SubResource("BTSequence_main")
