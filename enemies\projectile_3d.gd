extends RigidBody3D
class_name Projectile3D

@export var damage: float = 15.0
@export var speed: float = 15.0
@export var lifetime: float = 5.0

var direction: Vector3 = Vector3.FORWARD

func _ready() -> void:
	# Set up collision detection
	body_entered.connect(_on_body_entered)
	
	# Set initial velocity
	linear_velocity = direction * speed
	
	# Auto-destroy after lifetime
	var timer = Timer.new()
	add_child(timer)
	timer.wait_time = lifetime
	timer.one_shot = true
	timer.timeout.connect(queue_free)
	timer.start()

func set_direction(new_direction: Vector3) -> void:
	direction = new_direction.normalized()
	linear_velocity = direction * speed

func _on_body_entered(body: Node) -> void:
	# Don't hit the shooter
	if body.is_in_group("enemy"):
		return
	
	# Hit player
	if body.is_in_group("player"):
		if body.has_method("take_damage"):
			body.take_damage(damage)
		elif body.has_method("take_hit"):
			body.take_hit()
	
	# Destroy projectile on impact
	queue_free()
