[gd_scene load_steps=13 format=3 uid="uid://caic8bgayg0wd"]

[ext_resource type="Script" path="res://enemies/charger_enemy_3d.gd" id="1_script"]
[ext_resource type="BehaviorTree" uid="uid://cx8m7tuik8x3d" path="res://enemies/ai/trees/charger_enemy_3d.tres" id="1_bt"]
[ext_resource type="Shader" uid="uid://b3ejot0nxeca6" path="res://materials/wireframe.gdshader" id="2_shader"]
[ext_resource type="Texture2D" uid="uid://ih1vfqumfg3p" path="res://addons/prototype_mini_bundle/prototype_purple.png" id="3_texture"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_1"]

[sub_resource type="BoxMesh" id="BoxMesh_body"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_charger"]
render_priority = 0
shader = ExtResource("2_shader")
shader_parameter/modelColor = Color(0.2, 0.8, 0.2, 1)
shader_parameter/wireframeColor = Color(0.3, 1, 0.3, 1)
shader_parameter/width = 10.0
shader_parameter/modelOpacity = 0.7
shader_parameter/filtered = true

[sub_resource type="SphereMesh" id="SphereMesh_eye"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_eye"]
albedo_color = Color(0, 1, 0, 1)
emission_enabled = true
emission = Color(0, 0.5, 0, 1)

[sub_resource type="BlackboardPlan" id="BlackboardPlan_charger"]

[sub_resource type="SphereShape3D" id="SphereShape3D_attack"]
radius = 3.0

[sub_resource type="BoxShape3D" id="BoxShape3D_hurt"]
size = Vector3(1.4, 2.4, 1.4)

[node name="ChargerEnemy3D" type="CharacterBody3D" groups=["enemy"]]
script = ExtResource("1_script")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.0, 0)
shape = SubResource("CapsuleShape3D_1")

[node name="Model" type="Node3D" parent="."]

[node name="Body" type="MeshInstance3D" parent="Model"]
transform = Transform3D(1.2, 0, 0, 0, 2.2, 0, 0, 0, 1.2, 0, 1.0, 0)
mesh = SubResource("BoxMesh_body")
surface_material_override/0 = SubResource("ShaderMaterial_charger")

[node name="Eye" type="MeshInstance3D" parent="Model/Body"]
transform = Transform3D(0.2, 0, 0, 0, 0.1, 0, 0, 0, 0.2, 0, 0.3, -0.7)
mesh = SubResource("SphereMesh_eye")
surface_material_override/0 = SubResource("StandardMaterial3D_eye")

[node name="BTPlayer" type="BTPlayer" parent="."]
behavior_tree = ExtResource("1_bt")
blackboard_plan = SubResource("BlackboardPlan_charger")

[node name="NavigationAgent3D" type="NavigationAgent3D" parent="."]
simplify_path = true
debug_enabled = false

[node name="AttackArea" type="Area3D" parent="."]
collision_layer = 4
collision_mask = 1

[node name="CollisionShape3D" type="CollisionShape3D" parent="AttackArea"]
shape = SubResource("SphereShape3D_attack")

[node name="Hurtbox" type="Area3D" parent="."]
collision_layer = 2
collision_mask = 0

[node name="CollisionShape3D" type="CollisionShape3D" parent="Hurtbox"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.0, 0)
shape = SubResource("BoxShape3D_hurt")

[node name="HealthComponent" type="Node" parent="."]
script = GDScript.new()

[node name="AttackSystem" type="Node" parent="."]
script = GDScript.new()

[node name="DebugLabel" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 3.0, 0)
billboard = 1
text = "Charger"
font_size = 16
outline_size = 4
