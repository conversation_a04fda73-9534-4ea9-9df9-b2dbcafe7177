[gd_scene load_steps=6 format=3]

[ext_resource type="Script" path="res://enemies/projectile_3d.gd" id="1_script"]

[sub_resource type="SphereShape3D" id="SphereShape3D_1"]
radius = 0.1

[sub_resource type="SphereMesh" id="SphereMesh_1"]
radius = 0.1
height = 0.2

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1"]
albedo_color = Color(1, 0.5, 0, 1)
emission_enabled = true
emission = Color(1, 0.3, 0, 1)

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_1"]
gravity = Vector3(0, 0, 0)
initial_velocity_min = 2.0
initial_velocity_max = 5.0
scale_min = 0.1
scale_max = 0.3

[node name="Projectile3D" type="RigidBody3D"]
script = ExtResource("1_script")
collision_layer = 8
collision_mask = 1
gravity_scale = 0.0

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("SphereShape3D_1")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
mesh = SubResource("SphereMesh_1")
surface_material_override/0 = SubResource("StandardMaterial3D_1")

[node name="GPUParticles3D" type="GPUParticles3D" parent="."]
emitting = true
lifetime = 1.0
process_material = SubResource("ParticleProcessMaterial_1")
